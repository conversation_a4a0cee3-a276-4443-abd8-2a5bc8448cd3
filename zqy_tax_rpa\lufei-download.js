const { getherInvoice, downloadEXCEL, downloadPDF, getPDF, extractZIP, uploadPDF } = require('./Download.js')
const pbottleRPA = require("./pbottleRPA");
const { spawn } = require('child_process');
const Untils = require("./untils");
const untils = new Untils();

main()

async function main(){
    await getPDF({url:'jiangsu'})

    // await getPDF()
    // pbottleRPA.browserCMD_click('div.t-table__content > table > thead > tr > th > div > label > span')
    // await downloadEXCEL()
    // await downloadPDF()

    // 同时启动 zip.js 和 uploadPDF.js
    // const processes = await startBothScripts();

    // // 等待5秒
    // await pbottleRPA.sleep(15000);

    // // 关闭两个进程
    // await stopAllProcesses(processes);
}

// 同时执行两个脚本的函数
async function startBothScripts() {
    console.log('正在启动 zip.js 和 uploadPDF.js...');

    // 启动 zip.js 进程 - 配置实时输出
    const zipProcess = spawn('node', ['zip.js'], {
        cwd: __dirname,
        stdio: ['pipe', 'pipe', 'pipe'],
        env: {
            ...process.env,
            FORCE_COLOR: '1',
            NODE_NO_READLINE: '1' // 禁用 readline 缓冲
        }
    });

    // 启动 uploadPDF.js 进程 - 配置实时输出
    const uploadProcess = spawn('node', ['uploadPDF.js'], {
        cwd: __dirname,
        stdio: ['pipe', 'pipe', 'pipe'],
        env: {
            ...process.env,
            FORCE_COLOR: '1',
            NODE_NO_READLINE: '1' // 禁用 readline 缓冲
        }
    });

    // 处理 zip.js 的输出 - 实时输出
    zipProcess.stdout.setEncoding('utf8');
    zipProcess.stdout.on('data', (data) => {
        // 按行分割并实时输出
        const lines = data.toString().split('\n');
        lines.forEach(line => {
            if (line.trim()) {
                console.log(`[ZIP] ${line.trim()}`);
                // 强制刷新输出缓冲区
                process.stdout.write('');
            }
        });
    });

    zipProcess.stderr.setEncoding('utf8');
    zipProcess.stderr.on('data', (data) => {
        const lines = data.toString().split('\n');
        lines.forEach(line => {
            if (line.trim()) {
                console.error(`[ZIP ERROR] ${line.trim()}`);
                process.stderr.write('');
            }
        });
    });

    zipProcess.on('close', (code) => {
        console.log(`[ZIP] 进程退出，退出码: ${code}`);
    });

    // 处理 uploadPDF.js 的输出 - 实时输出
    uploadProcess.stdout.setEncoding('utf8');
    uploadProcess.stdout.on('data', (data) => {
        const lines = data.toString().split('\n');
        lines.forEach(line => {
            if (line.trim()) {
                console.log(`[UPLOAD] ${line.trim()}`);
                process.stdout.write('');
            }
        });
    });

    uploadProcess.stderr.setEncoding('utf8');
    uploadProcess.stderr.on('data', (data) => {
        const lines = data.toString().split('\n');
        lines.forEach(line => {
            if (line.trim()) {
                console.error(`[UPLOAD ERROR] ${line.trim()}`);
                process.stderr.write('');
            }
        });
    });

    uploadProcess.on('close', (code) => {
        console.log(`[UPLOAD] 进程退出，退出码: ${code}`);
    });

    // 处理进程错误
    zipProcess.on('error', (err) => {
        console.error(`[ZIP] 启动进程时出错: ${err}`);
    });

    uploadProcess.on('error', (err) => {
        console.error(`[UPLOAD] 启动进程时出错: ${err}`);
    });

    console.log('两个脚本已启动，正在后台运行...');

    // 返回进程对象，以便外部控制
    return {
        zipProcess: zipProcess,
        uploadProcess: uploadProcess
    };
}

// 停止所有进程的函数
async function stopAllProcesses(processes) {
    console.log('正在关闭所有进程...');

    const stopPromises = [];

    // 停止 ZIP 进程
    if (processes.zipProcess) {
        stopPromises.push(stopSingleProcess(processes.zipProcess, 'ZIP'));
    }

    // 停止 UPLOAD 进程
    if (processes.uploadProcess) {
        stopPromises.push(stopSingleProcess(processes.uploadProcess, 'UPLOAD'));
    }

    // 等待所有进程停止
    await Promise.all(stopPromises);
    console.log('所有进程已终止');
}

// 停止单个进程的函数
async function stopSingleProcess(process, processName, timeout = 3000) {
    return new Promise((resolve) => {
        if (!process || process.killed) {
            console.log(`${processName} 进程已经停止`);
            resolve();
            return;
        }

        console.log(`终止 ${processName} 进程...`);

        // 监听进程关闭事件
        const onClose = () => {
            console.log(`${processName} 进程已关闭`);
            resolve();
        };

        process.once('close', onClose);

        // 发送终止信号
        process.kill('SIGTERM');

        // 设置超时，如果进程在指定时间内没有停止，强制杀死
        setTimeout(() => {
            if (!process.killed) {
                console.log(`强制杀死 ${processName} 进程`);
                process.removeListener('close', onClose);
                process.kill('SIGKILL');

                // 再等待一点时间确保进程被杀死
                setTimeout(() => {
                    resolve();
                }, 500);
            }
        }, timeout);
    });
}

