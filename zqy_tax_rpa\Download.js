const Untils = require("./untils");
const untils = new Untils();
const pbottleRPA = require("./pbottleRPA");
const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');
const AdmZip = require('adm-zip');
const xlsx = require("node-xlsx");

// 解析配置文件
const workSheetsFromFile = xlsx.parse(`${__dirname}\\配置项.xlsx`);
const config_sheet = workSheetsFromFile[0].data;

// 指定要监听的文件夹
const folderPath = config_sheet[1][2];

const OSS = require('ali-oss');
const ossUrl = 'https://zqcloud.shuzutech.com';
// OSS配置
const client = new OSS({
    endpoint: 'oss-cn-shanghai.aliyuncs.com',
    accessKeyId: 'LTAI5tEZoVaq5zroB7urbauK',
    accessKeySecret: '******************************',
    bucket: 'zhenqi-cloud',
});


let getherInvoice = async () => {
    const begin = 1699771103000 
    const end = 1762843103000
    const times = calculateYear(begin,end)
    console.log("进入gather_invoice");
    pbottleRPA.openURL(
        `https://dppt.jiangsu.chinatax.gov.cn:8443/invoice-query/invoice-query/`
    );
    let downloaStartTime = new Date().getTime();
    console.log('downloaStartTime',downloaStartTime)
    var count = 0;
    let chaxuntype = await untils.waitImage("/input/1920/chaxuntype.png");
    if (chaxuntype) {
        pbottleRPA.moveMouseSmooth(chaxuntype.x, chaxuntype.y);
    }
    for (let index = 0; index < 2; index++) {
        if (index == 1) {
        //chaxuntype.png
        pbottleRPA.moveMouseSmooth(chaxuntype.x + 150, chaxuntype.y);
        pbottleRPA.mouseClick(); //focus
        pbottleRPA.browserCMD_click(`li span:contains(取得发票)`);
        }

        const fplx = index === 0 ? "销项" : "进项";
        // 获取采集截至日期
        var currentDate = ''
        if(end){
            currentDate = new Date(end) 
        }else{
            currentDate = new Date();
             // 获取当前日期的上一个月
            currentDate.setMonth(currentDate.getMonth() - 1);
        }

        // 循环输出3年的数据
        for (var i = 1; i <= times; i++) {
        let currentCount = 0;
        if (index == 1 && count != currentCount) {
            //chaxuntype.png
            currentCount = count;
            pbottleRPA.moveMouseSmooth(chaxuntype.x + 150, chaxuntype.y);
            pbottleRPA.mouseClick(); //focus
            pbottleRPA.browserCMD_click(`li span:contains(取得发票)`);
        }
       if (begin && end) {
			if( i == 1 ){
				var startDate = new Date(
					currentDate.getFullYear()-1,
					currentDate.getMonth(),
					currentDate.getDate() + 1 
				)
				var endDate = new Date(end)
			}else{
				var startDate = new Date(
					currentDate.getFullYear(),
					currentDate.getMonth(),
					currentDate.getDate() + 1 
				)
				var endDate = new Date(
					currentDate.getFullYear(),
					currentDate.getMonth() ,
					currentDate.getDate()
				);
			}
			if( i == times ){
				var startDate = new Date(begin)
			}else{
				var endDate = new Date(
					currentDate.getFullYear(),
					currentDate.getMonth() ,
					currentDate.getDate()
				);
			}
	
		}else{
			// 获取11个月前的日期
			var startDate = new Date(
				currentDate.getFullYear(),
				currentDate.getMonth() - 11,
				1
			);
			// 获取当前月份的最后一天
			var endDate = new Date(
				currentDate.getFullYear(),
				currentDate.getMonth() + 1,
				0
			);
		}
		
		// 输出日期范围
		console.log(formatDate(startDate) + "---------->" + formatDate(endDate));

        // 将当前日期往前推12个月
        currentDate.setMonth(currentDate.getMonth() - 12);

        // 调用查询和下载函数，并累加count值
        const additionalCount = await queryAndDownloadByDateRange(startDate, endDate, fplx, chaxuntype, index);
        count += additionalCount;
        }
    }
    console.log("count+++++++++++++++++++++++++++++", count)
    if (count > 0) {
        downloadEXCEL(count,downloaStartTime,'卜宁川','jiangsu')
    }
};

let downloadEXCEL = async (count,startTime,xm,province) => {
    pbottleRPA.moveMouseSmooth(1000,300)
    pbottleRPA.mouseClick()
    pbottleRPA.openURL(`https://dppt.${province}.chinatax.gov.cn:8443/importing-exporting-batches-search`)
    const chaxun = await untils.existImage('/input/1920/chaxun.png')
    if(chaxun){
        // 选择任务状态
        pbottleRPA.browserCMD_click('body > section > section > section > main > div.g-layout-main__content > div > div.hide-side-layout__container-content > div > div.hide-side-layout__page-content > div > div.search_header > form > div > div > div:nth-child(1) > div > div.t-form__controls > div > div > div > div > div')
        pbottleRPA.browserCMD_click('body > div:nth-child(8) > div > div > div > ul.t-select__list.t-all-list > li > label')
        pbottleRPA.browserCMD_click('body > div:nth-child(8) > div > div > div > ul:nth-child(2) > li:nth-child(3)')
        // 选择当前日期
        const currentDate = new Date()
        pbottleRPA.browserCMD_click(`input[placeholder="请选择"]`)
        pbottleRPA.browserCMD_val('input[placeholder="请选择"]', formatDate(currentDate))
        pbottleRPA.keyTap('Enter')
        // 选择任务所属功能
        pbottleRPA.browserCMD_click('body > section > section > section > main > div.g-layout-main__content > div > div.hide-side-layout__container-content > div > div.hide-side-layout__page-content > div > div.search_header > form > div > div > div:nth-child(4) > div > div.t-form__controls > div > div > div > div > div')
        pbottleRPA.browserCMD_click('body > div:nth-child(10) > div > div.t-popup__content > div > ul.t-select__list.t-all-list > li > label')
        pbottleRPA.browserCMD_click('body > div:nth-child(10) > div > div > div > ul:nth-child(2) > li:nth-child(4)')
        pbottleRPA.mouseClick()
        // 点击查询
        pbottleRPA.browserCMD_click('body > section > section > section > main > div.g-layout-main__content > div > div.hide-side-layout__container-content > div > div.hide-side-layout__page-content > div > div.t-loading__parent > div.project-table__container > div > div:nth-child(4) > div > div > div > div > div.t-select__wrap.t-pagination__select > div > div')
        pbottleRPA.browserCMD_click('body > div:nth-child(11) > div > div > div > ul > li:nth-child(3)')
        pbottleRPA.browserCMD_click('body > section > section > section > main > div.g-layout-main__content > div > div.hide-side-layout__container-content > div > div.hide-side-layout__page-content > div > div.search_header > form > div > div > div:nth-child(8) > div > div > div > button.t-button.t-button--variant-base.t-button--theme-primary')
        await handleDownloadData(count,startTime,xm,province)
    }else{
        return await downloadEXCEL(count,startTime,xm,province)
    }
};

const handleDownloadData = async (count,startTime,xm,province) => {
    console.log('当前办税员姓名',xm)
    let downloadData = [];
    let total = pbottleRPA.browserCMD_text("div.t-pagination__total");
    total = Number(total.replace("共", "").replace("条", ""));
    console.log("total", total);
    if (total > 0) {
        for (let n = 1; n <= total; n++) {
        // 当数据条数和异步任务的数量一致时，退出循环开始下载
        if(downloadData.length == count){
            console.log('downloadData',downloadData)
            for(let i = 1; i <= downloadData.length; i++){
                // console.log(`开始下载第${i}个文件`)
                pbottleRPA.browserCMD_click(
                    `div[class="t-table__content"] tr:nth-child(${downloadData[i-1].n}) span[class="operate"]`
                )
                console.log(`第${i}个文件下载完成`)
                untils.addLog(global.traceId,`批量任务共${downloadData.length}个文件待下载,第${i}个文件下载完成`,"gather_invoice")
            }
            console.log('全部下载完成')
            untils.addLog(global.traceId,`批量任务共${downloadData.length}个文件全部下载完成`,"gather_invoice")
            break;
        }  
        const id = pbottleRPA
            .browserCMD_text(
            `div[class="t-table__content"] tbody tr:nth-child(${n}) td:nth-child(1)`
            )
            .trim();
        const submitter = pbottleRPA
            .browserCMD_text(
            `div[class="t-table__content"] tbody tr:nth-child(${n}) td:nth-child(3)`
            )
            .trim();
        const submitTime = pbottleRPA
            .browserCMD_text(
            `div[class="t-table__content"] tbody tr:nth-child(${n}) td:nth-child(5)`
            )
            .trim();
        const status = pbottleRPA
            .browserCMD_text(
            `div[class="t-table__content"] tbody tr:nth-child(${n}) td:nth-child(7)`
            )
            .trim();
        const downloadButton = pbottleRPA
            .browserCMD_text(
            `div[class="t-table__content"] tbody tr:nth-child(${n}) td:nth-child(11)`
            )
            .trim();
        const submitTimeISO = submitTime.replace(" ", "T"); // 将空格替换为 'T'
        const submitTimeTimestamp = new Date(submitTimeISO).getTime();
        console.log(
            "00000000000",
            id,
            submitter,
            submitTime,
            submitTimeTimestamp,
            status,
            downloadButton
        );
        console.log('startTime submitTimeTimestamp',startTime,submitTimeTimestamp)
        if (startTime < submitTimeTimestamp ) {
            if(submitter == xm){
                console.log('本人记录')
                downloadData.push({
                    id,
                    submitter,
                    submitTime,
                    submitTimeTimestamp,
                    status,
                    downloadButton,
                    n
                })
            }else{
                console.log('非本人记录')
            }
        } else {
            console.log("数据已过期");
            await refresh()
            break;
        }}
    } else {
        console.log("无数据");
        await refresh()
    }

    async function refresh(){
        pbottleRPA.browserCMD_click(
            "body > section > section > section > main > div.g-layout-main__content > div > div.hide-side-layout__container-content > div > div.hide-side-layout__page-content > div > div.search_header > form > div > div > div:nth-child(8) > div > div > div > button.t-button.t-button--variant-base.t-button--theme-primary"
        );
        await handleDownloadData(count,startTime,xm,province);
    }
};

const getPDF = async (proItem,childrenTask) => {
    untils.addLog(global.traceId,`开始下载PDF`,"download_PDF")
    pbottleRPA.openURL(
        `https://dppt.${proItem.url}.chinatax.gov.cn:8443/invoice-query/invoice-query/`
    );
    let chaxuntype = await untils.waitImage("/input/1920/chaxuntype.png")
    if (chaxuntype) {
        pbottleRPA.moveMouseSmooth(chaxuntype.x, chaxuntype.y);
    }
    let count = 0
    pbottleRPA.browserCMD_click('body > section > section > section > main > div.g-layout-main__content > div > div.hide-side-layout__container-content > div > div.hide-side-layout__page-content > div > div.t-row.t-row--start.t-row--align-top.search__box > div > form > div > div > div > div:nth-child(4) > div > div.t-form__controls > div > div > div > div > div')
    pbottleRPA.browserCMD_click('body > div:nth-child(19) > div > div > div > ul:nth-child(2) > li:nth-child(1)')
    pbottleRPA.browserCMD_click('body > div:nth-child(19) > div > div > div > ul:nth-child(2) > li:nth-child(2)')
    pbottleRPA.browserCMD_click('body > div:nth-child(19) > div > div > div > ul:nth-child(2) > li:nth-child(3)')
    pbottleRPA.browserCMD_click('body > div:nth-child(19) > div > div > div > ul:nth-child(2) > li:nth-child(4)')
    pbottleRPA.browserCMD_click('body > div:nth-child(19) > div > div > div > ul:nth-child(2) > li:nth-child(17)')
    pbottleRPA.browserCMD_click('body > div:nth-child(19) > div > div > div > ul:nth-child(2) > li:nth-child(18)')
    pbottleRPA.browserCMD_click('body > div:nth-child(19) > div > div > div > ul:nth-child(2) > li:nth-child(19)')
    pbottleRPA.browserCMD_click('div.t-select__wrap.t-pagination__select > div > div > div')
    pbottleRPA.browserCMD_click('body > div:nth-child(20) > div > div > div > ul > li:nth-child(4)')
    pbottleRPA.mouseClick()
    for (let index = 0; index < 2; index++) {
        if (index == 1) {
        //chaxuntype.png
        pbottleRPA.moveMouseSmooth(chaxuntype.x + 150, chaxuntype.y);
        pbottleRPA.mouseClick(); //focus
        pbottleRPA.browserCMD_click(`li span:contains(取得发票)`);
        }

        const fplx = index === 0 ? "销项" : "进项";
        // 获取当前日期
        var currentDate = new Date();

        // 获取当前日期的上一个月
        currentDate.setMonth(currentDate.getMonth() - 1);
        // 循环输出3年的数据
        for (var i = 0; i < 3; i++) {
        
        // 获取11个月前的日期
        var startDate = new Date(
            currentDate.getFullYear(),
            currentDate.getMonth() - 11,
            1
        );

        // 获取当前月份的最后一天
        var endDate = new Date(
            currentDate.getFullYear(),
            currentDate.getMonth() + 1,
            0
        );

        // 输出日期范围
        console.log(formatDate(startDate) + "---------->" + formatDate(endDate));

        // 将当前日期往前推12个月
        currentDate.setMonth(currentDate.getMonth() - 12);

        pbottleRPA.browserCMD_click(`input[placeholder="开票日期起"]`);
        pbottleRPA.browserCMD_val(
            'input[placeholder="开票日期起"]',
            formatDate(startDate)
        );
        pbottleRPA.keyTap("Enter");

        pbottleRPA.browserCMD_click(`input[placeholder="开票日期止"]`);
        pbottleRPA.browserCMD_val(
            'input[placeholder="开票日期止"]',
            formatDate(endDate)
        );
        pbottleRPA.keyTap("Enter");

        pbottleRPA.browserCMD_click(`button span:contains(查询)`);
        console.log("-------------查询结束---------------");
        pbottleRPA.sleep(1000);
        let daochu = pbottleRPA.browserCMD_text(
            'div[class="button__export"] > button > span:nth-child(1)'
        );
        if (daochu === "ok") {
            console.log("-------------查询无发票数据--------------");
            console.log(
            `${formatDate(startDate)}` +
                "---" +
                `${formatDate(endDate)}` +
                "年度无" +
                `${fplx}` +
                "数据"
            );
            untils.addLog(
            global.traceId,
            `${formatDate(startDate)}` +
                "---" +
                `${formatDate(endDate)}` +
                "年度无" +
                `${fplx}` +
                "数据",
            "download_PDF"
            );
        } else {
            pbottleRPA.sleep(500);
            let text = pbottleRPA.browserCMD_text(
            `div[class="t-pagination__total"]`
            ); //div[class = "statistics-info"] span:nth-child(1)
            let match = text.match(/\d+/);
            let number = match ? parseInt(match[0]) : 0;
            console.log("===============", text, number);
            for(let n = 0; n < Math.ceil(number/100); n++){
                pbottleRPA.sleep(1000);
                pbottleRPA.browserCMD_click('div.t-table__content > table > thead > tr > th > div > label > span')
                pbottleRPA.browserCMD_click(`button span:contains(下载)`);
                pbottleRPA.browserCMD_click(`li span:contains(下载选中)`);
                let wjxzlx = await untils.existImage("/input/1920/wjxzlx.png")
                if(wjxzlx){
                    let downloadPDF = await untils.existImage2("/input/1920/downloadPDF.png")
                    if(!downloadPDF){
                        pbottleRPA.browserCMD_click('div.t-dialog__body.t-dialog__body__icon > div > form > div > div > div > div > label:nth-child(2) > span.t-checkbox__input')
                    }
                    pbottleRPA.browserCMD_click('div.t-dialog__footer > button:nth-child(2)')
                    let dccg = await untils.existImage("/input/1920/dccg.png")
                    if(dccg){
                        pbottleRPA.browserCMD_click('body > section > section > section > main > div.g-layout-main__content > div > div.hide-side-layout__container-content > div > div.hide-side-layout__page-content > div > div:nth-child(8) > div > div.t-dialog__wrap > div > div > div.t-dialog__footer > div > button') 
                        count++
                        console.log(`${formatDate(startDate)}` +"---" + `${formatDate(endDate)}` + "年度" +`${fplx}` +"数据," +`第${n+1}页数据下载成功`);
                        untils.addLog(global.traceId,`${formatDate(startDate)}` +"---" + `${formatDate(endDate)}` + "年度" +`${fplx}` +"数据," +`第${n+1}页数据下载成功`,"download_PDF")
                    }else{
                        console.log(`${formatDate(startDate)}` +"---" + `${formatDate(endDate)}` + "年度" +`${fplx}` +"数据," +`第${n+1}页数据下载失败`);
                        untils.addLog(global.traceId,`${formatDate(startDate)}` +"---" + `${formatDate(endDate)}` + "年度" +`${fplx}` +"数据," +`第${n+1}页数据下载失败`,"download_PDF")
                    }
                }else{
                    console.log(`${formatDate(startDate)}` +"---" + `${formatDate(endDate)}` + "年度" +`${fplx}` +"数据," +`第${n+1}页数据下载失败`);
                    untils.addLog(global.traceId,`${formatDate(startDate)}` +"---" + `${formatDate(endDate)}` + "年度" +`${fplx}` +"数据," +`第${n+1}页数据下载失败`,"download_PDF")
                }
                await pbottleRPA.browserCMD_click('body > section > section > section > main > div.g-layout-main__content > div > div.hide-side-layout__container-content > div > div.hide-side-layout__page-content > div > div:nth-child(4) > div:nth-child(2) > div > div > div:nth-child(4) > div.t-affix > div > div > div > div.t-pagination__btn.t-pagination__btn-next')
            }
        }
    }}
    await downloadPDF(proItem.url,count,childrenTask)
}
    
const downloadPDF = async (province,count,childrenTask) => {
    pbottleRPA.moveMouseSmooth(1000,300)
    pbottleRPA.mouseClick()
    pbottleRPA.openURL(`https://dppt.${province}.chinatax.gov.cn:8443/importing-exporting-batches-search`)
    const chaxun = await untils.existImage('/input/1920/chaxun.png')
    if(chaxun){
        // 选择任务状态
        pbottleRPA.browserCMD_click('body > section > section > section > main > div.g-layout-main__content > div > div.hide-side-layout__container-content > div > div.hide-side-layout__page-content > div > div.search_header > form > div > div > div:nth-child(1) > div > div.t-form__controls > div > div > div > div > div')
        pbottleRPA.browserCMD_click('body > div:nth-child(8) > div > div > div > ul.t-select__list.t-all-list > li > label')
        pbottleRPA.browserCMD_click('body > div:nth-child(8) > div > div > div > ul:nth-child(2) > li:nth-child(3)')
        // 选择当前日期
        const currentDate = new Date()
        pbottleRPA.browserCMD_click(`input[placeholder="请选择"]`)
        pbottleRPA.browserCMD_val('input[placeholder="请选择"]', formatDate(currentDate))
        pbottleRPA.keyTap('Enter')
        // 选择任务所属功能
        pbottleRPA.browserCMD_click('body > section > section > section > main > div.g-layout-main__content > div > div.hide-side-layout__container-content > div > div.hide-side-layout__page-content > div > div.search_header > form > div > div > div:nth-child(4) > div > div.t-form__controls > div > div > div > div > div')
        pbottleRPA.browserCMD_click('body > div:nth-child(10) > div > div.t-popup__content > div > ul.t-select__list.t-all-list > li > label')
        pbottleRPA.browserCMD_click('body > div:nth-child(10) > div > div > div > ul:nth-child(2) > li:nth-child(5)')
        pbottleRPA.mouseClick()
        // 点击查询
        pbottleRPA.browserCMD_click('body > section > section > section > main > div.g-layout-main__content > div > div.hide-side-layout__container-content > div > div.hide-side-layout__page-content > div > div.t-loading__parent > div.project-table__container > div > div:nth-child(4) > div > div > div > div > div.t-select__wrap.t-pagination__select > div > div')
        pbottleRPA.browserCMD_click('body > div:nth-child(11) > div > div > div > ul > li:nth-child(3)')
        pbottleRPA.browserCMD_click('body > section > section > section > main > div.g-layout-main__content > div > div.hide-side-layout__container-content > div > div.hide-side-layout__page-content > div > div.search_header > form > div > div > div:nth-child(8) > div > div > div > button.t-button.t-button--variant-base.t-button--theme-primary')
        await handleDownloadPDF(count,childrenTask) 
    }else{
        await downloadPDF(province,count,childrenTask)
    }
}

const handleDownloadPDF = async (count,childrenTask) => {
    let total = pbottleRPA.browserCMD_text("div.t-pagination__total");
    total = Number(total.replace("共", "").replace("条", ""));
    console.log("total", total);
    console.log("count", count);
    untils.addLog(global.traceId, `共${total}条数据，下载${count}条数据`, "download_PDF");
    if(total >= count){ 
        // 先删除文件夹里面的文件
        // const folderPath = config_sheet[1][2];
        if (fs.existsSync(folderPath)) {
            fs.readdirSync(folderPath).forEach(file => {
                const curPath = path.join(folderPath, file);
                if (fs.lstatSync(curPath).isDirectory()) { // 判断是否是文件夹
                    deleteFolder(curPath); // 递归删除文件夹
                } else { // 不是文件夹，删除文件
                    fs.unlinkSync(curPath);
                }
            });
        }
        for(let i = 1; i <=  Math.ceil(count/50); i++){
            if(i == Math.ceil(count/50) && count%50 != 0){
                for(let j = 1; j <= count%50; j++){
                    pbottleRPA.browserCMD_click(`div[class="t-table__content"] tr:nth-child(${j}) span[class="operate"]`)
                    console.log(`正在下载第${50*(i-1)+j}个文件`);
                    let down1 = await untils.waitImageDisappear("/input/1920/dowloading.png")
                    if(down1){
                        try{
                            pbottleRPA.sleep(2000)
                            await extractZIP()
                            pbottleRPA.sleep(2000)
                            await uploadPDF() 
                        }catch(e){}
                    }
                }
            }else{
                for(let j = 1; j <= 50; j++){
                    pbottleRPA.browserCMD_click(`div[class="t-table__content"] tr:nth-child(${j}) span[class="operate"]`)
                    console.log(`正在下载第${50*(i-1)+j}个文件`);
                    let down1 = await untils.waitImageDisappear("/input/1920/dowloading.png")
                    if(down1){
                        try{
                            pbottleRPA.sleep(2000)
                            await extractZIP()
                            pbottleRPA.sleep(2000)
                            await uploadPDF() 
                        }catch(e){} 
                    }
                }
                pbottleRPA.browserCMD_click('div.t-affix > div > div > div > div.t-pagination__btn.t-pagination__btn-next')
            }
        }
        untils.completeTask(
            childrenTask.id,
            childrenTask.flowId,
            childrenTask.flowKey,
            childrenTask.taskKey,
            childrenTask.variable,
            1,
            `PDF下载上传成功`
        )
    }else{
        refresh(count,childrenTask)
    }

    function refresh(count,childrenTask){
        pbottleRPA.browserCMD_click(
            "body > section > section > section > main > div.g-layout-main__content > div > div.hide-side-layout__container-content > div > div.hide-side-layout__page-content > div > div.search_header > form > div > div > div:nth-child(8) > div > div > div > button.t-button.t-button--variant-base.t-button--theme-primary"
        );
        handleDownloadPDF(count,childrenTask);
    }
    function deleteFolder(path) {
        if (fs.existsSync(path)) {
            fs.readdirSync(path).forEach(file => {
                const curPath = path + '/' + file;
                if (fs.lstatSync(curPath).isDirectory()) { 
                    deleteFolder(curPath); 
                } else { 
                    fs.unlinkSync(curPath);
                }
            });
            fs.rmdirSync(path);
        }
    }
}

const calculateYear = (startTime, endTime) => {
    if(startTime,endTime){
         // 将时间戳转换为Date对象
        const startDate = new Date(startTime);
        const endDate = new Date(endTime);
        // 获取年份
        const starYear = startDate.getFullYear();
        const endYear = endDate.getFullYear();
        // 计算年份差
        let yearDifference = endYear - starYear;
        // 检查是否跨年但不足一年的情况
        if (endDate.getMonth() < startDate.getMonth() || (endDate.getMonth() === startDate.getMonth() && endDate.getDate() < startDate.getDate())) {
            yearDifference--;
        }
        // 年份差加一
        yearDifference++;
        console.log('年份差（加一后）:', yearDifference);
        return yearDifference;
    }else{
        return 3
	}
}

const formatDate = (date) => {
    var year = date.getFullYear();
    var month = padZero(date.getMonth() + 1);
    var day = padZero(date.getDate());
    return year + "-" + month + "-" + day;
}

// 给小于10的数字前面补0
const padZero = (num) => {
    return num < 10 ? "0" + num : "" + num;
}

// 递归查询和下载函数，用于处理大量发票数据的日期分割
const queryAndDownloadByDateRange = async (startDate, endDate, fplx, chaxuntype, index) => {
    console.log(`开始查询日期范围: ${formatDate(startDate)} 到 ${formatDate(endDate)}, 发票类型: ${fplx}`);

    // 设置日期范围
    pbottleRPA.browserCMD_click(`input[placeholder="开票日期起"]`);
    pbottleRPA.browserCMD_val(
        'input[placeholder="开票日期起"]',
        formatDate(startDate)
    );
    pbottleRPA.keyTap("Enter");

    pbottleRPA.browserCMD_click(`input[placeholder="开票日期止"]`);
    pbottleRPA.browserCMD_val(
        'input[placeholder="开票日期止"]',
        formatDate(endDate)
    );
    pbottleRPA.keyTap("Enter");

    pbottleRPA.browserCMD_click(`button span:contains(查询)`);
    console.log("-------------查询结束---------------");
    pbottleRPA.sleep(1000);

    let daochu = pbottleRPA.browserCMD_text(
        'div[class="button__export"] > button > span:nth-child(1)'
    );

    if (daochu === "ok") {
        console.log("-------------查询无发票数据--------------");
        console.log(
            `${formatDate(startDate)}` +
                "---" +
                `${formatDate(endDate)}` +
                "年度无" +
                `${fplx}` +
                "数据"
        );
        untils.addLog(
            global.traceId,
            `${formatDate(startDate)}` +
                "---" +
                `${formatDate(endDate)}` +
                "年度无" +
                `${fplx}` +
                "数据",
            "gather_invoice"
        );
        return 0; // 无数据时返回0
    }

    pbottleRPA.sleep(500);
    let text = pbottleRPA.browserCMD_text(
        `div[class="t-pagination__total"]`
    );
    let match = text.match(/\d+/);
    let number = match ? parseInt(match[0]) : 0;
    console.log("===============", text, number);

    // 如果数量仍然大于等于100000，继续分割
    if (number >= 100000) {
        console.log("-------------数量仍然过多，继续分割日期范围---------------");

        // 计算中间日期
        const startTime = startDate.getTime();
        const endTime = endDate.getTime();
        const midTime = Math.floor((startTime + endTime) / 2);
        const midDate = new Date(midTime);

        console.log(`继续分割: ${formatDate(startDate)} 到 ${formatDate(endDate)}`);
        console.log(`分割为: ${formatDate(startDate)} 到 ${formatDate(midDate)} 和 ${formatDate(new Date(midTime + 24 * 60 * 60 * 1000))} 到 ${formatDate(endDate)}`);

        // 递归查询第一个日期范围，并累加count
        const count1 = await queryAndDownloadByDateRange(startDate, midDate, fplx, chaxuntype, index);

        // 递归查询第二个日期范围，并累加count
        const nextDay = new Date(midTime + 24 * 60 * 60 * 1000);
        const count2 = await queryAndDownloadByDateRange(nextDay, endDate, fplx, chaxuntype, index);

        // 返回两个递归调用的count总和
        return count1 + count2;
    }

    // 数量合适，开始下载
    pbottleRPA.browserCMD_click(`button span:contains(导出 )`);
    pbottleRPA.browserCMD_click(`li span:contains(导出全部)`);
    let downloading = await untils.existImage("/input/1920/dowloading.png");

    let countIncrement = 0; // 用于记录当前下载任务的count增量

    if (downloading) {
        console.log("-------------直接开始下载---------------");
    } else if (!downloading) {
        countIncrement = 1; // 异步下载时count增加1
        // 发票数量大于2500，异步下载
        console.log("-------------提交异步下载---------------");
        if (!downloading) {
            console.log("温馨提示");
            let wxts1 = await untils.waitImage("/input/1920/wxts.png");
            pbottleRPA.sleep(500);
            pbottleRPA.moveMouseSmooth(wxts1.x + 20, wxts1.y + 180);
            pbottleRPA.mouseClick(); //focus
        }
        untils.addLog(
            global.traceId,
            `${formatDate(startDate)}` +
            "---" +
            `${formatDate(endDate)}` +
            `${fplx}` +
            "数据提交异步下载",
            "gather_invoice"
        );
        pbottleRPA.sleep("30000");
        pbottleRPA.keyTap("alt + Left");
    }

    let down1 = await untils.waitImageDisappear(
        "/input/1920/dowloading.png"
    );
    if (down1) {
        console.log("-------------已下载---------------");
        untils.addLog(
            global.traceId,
            `${formatDate(startDate)}` +
            "---" +
            `${formatDate(endDate)}` +
            `${fplx}` +
            "数据下载完成",
            "gather_invoice"
        );
        pbottleRPA.sleep(2000);
    }

    // 返回count的增量值
    return countIncrement;
}

const extractZIP = () => {
    return new Promise((resolve, reject) => {
        // 处理 zip 文件的函数，包含重试机制
        async function processZipFile(filePath, filename, retryCount = 0) {
            const maxRetries = 3;
            const retryDelay = 2000; // 2秒

            try {
                // 检查文件是否仍然存在
                if (!fs.existsSync(filePath)) {
                    console.log(`文件不存在，跳过处理: ${filename}`);
                    return;
                }

                // 尝试打开文件以检查是否可以访问
                const fileHandle = await fs.promises.open(filePath, 'r');
                await fileHandle.close();

                console.log(`开始处理压缩文件: ${filename} (尝试 ${retryCount + 1}/${maxRetries + 1})`);

                // 创建一个新的 AdmZip 实例
                const zip = new AdmZip(filePath);

                // 解压文件到指定的文件夹
                zip.extractAllTo(folderPath, true);
                console.log(`解压完成: ${filename}`);
                untils.addLogForPDF('uploadPDF', `解压完成: ${filename}`, 'uploadPDF')

                // 解压完成后删除压缩文件
                fs.unlinkSync(filePath);
                console.log(`已删除压缩文件: ${filename}`);
            } catch (err) {
                console.error(`处理压缩文件时出错 (尝试 ${retryCount + 1}): ${err.message}`);

                if (retryCount < maxRetries) {
                    setTimeout(() => {
                        processZipFile(filePath, filename, retryCount + 1);
                    }, retryDelay);
                } else {
                    console.error(`处理文件失败，已达到最大重试次数: ${filename}`);
                }
            }
        }

        // 读取文件夹中的所有文件
        fs.readdir(folderPath, async (err, files) => {
            if (err) {
                console.error(`读取文件夹时出错: ${err}`);
                reject(err);
                return;
            }

            // 过滤出所有的 zip 文件
            const zipFiles = files.filter(file => path.extname(file).toLowerCase() === '.zip');

            console.log(`文件夹中有 ${zipFiles.length} 个 zip 文件`);

            if (zipFiles.length === 0) {
                console.log('没有找到zip文件，extractZIP完成');
                resolve();
                return;
            }

            // 处理所有zip文件的Promise数组
            const processPromises = zipFiles.map(filename => {
                return new Promise((resolveFile) => {
                    const filePath = path.join(folderPath, filename);
                    console.log(`检测到新的压缩文件: ${filename}`);
                    setTimeout(async () => {
                        await processZipFile(filePath, filename);
                        resolveFile();
                    }, 1000); // 延迟1秒等待文件完全写入
                });
            });

            // 等待所有zip文件处理完成
            try {
                await Promise.all(processPromises);
                console.log('所有zip文件处理完成');
                resolve();
            } catch (error) {
                console.error('处理zip文件时出错:', error);
                reject(error);
            }
        });
    });
}

const uploadPDF = () => {
    return new Promise((resolve, reject) => {
        console.log(`正在上传文件夹: ${folderPath} 中的PDF文件`);

        // 读取文件夹中的所有文件
        fs.readdir(folderPath, async (err, files) => {
            if (err) {
                console.error(`读取文件夹时出错: ${err}`);
                reject(err);
                return;
            }

            try {
                for (const file of files) {
                    const filePath = path.join(folderPath, file);

                    // 检查文件是否为PDF文件
                    if (path.extname(file).toLowerCase() === '.pdf') {
                        console.log(`检测到新的PDF文件: ${file}`);
                        // 找到最后一个下划线的位置
                        const lastUnderscoreIndex = file.lastIndexOf('_');
                        // 找到文件扩展名 .pdf 的位置
                        const fileExtensionIndex = file.lastIndexOf('.pdf');
                        // 截取最后一个下划线之前的部分，并加上文件扩展名
                        const newFilename = file.substring(0, lastUnderscoreIndex) + file.substring(fileExtensionIndex);
                        console.log(newFilename);
                        // 重命名文件
                        const fileName = '/eInvoice/' + newFilename;
                        let fileUrl = ossUrl + fileName;
                        try {
                            await client.put(fileName, filePath);
                            console.log('文件上传成功:', fileUrl);
                            untils.addLogForPDF('uploadPDF', '上传PDF文件成功:' + fileUrl, 'uploadPDF');
                            // 上传成功后删除文件
                            await fs.promises.unlink(filePath);
                            console.log('文件已删除:', filePath);
                        } catch (error) {
                            console.error('文件上传失败:', error);
                            // 失败之后删除文件
                            await fs.promises.unlink(filePath);
                            untils.addLogForPDF('uploadPDF', '上传PDF文件失败:' + fileUrl + '失败原因:' + error, 'uploadPDF');
                        }
                    } else if (path.extname(file).toLowerCase() === '.xlsx') {
                        console.log(`检测到xlsx文件: ${file}`);
                        untils.addLogForPDF('uploadPDF', 'xlsx文件:' + file, 'uploadPDF');
                        // 删除xlsx文件
                        await fs.promises.unlink(filePath);
                        console.log('文件已删除:', filePath);
                    }
                }

                console.log('uploadPDF 处理完成');
                resolve();
            } catch (error) {
                console.error('uploadPDF 处理过程中出错:', error);
                reject(error);
            }
        });
    });
}

module.exports = {
    getherInvoice,
    downloadEXCEL,
    getPDF,
    downloadPDF,
    formatDate,
    padZero,
    queryAndDownloadByDateRange,
    extractZIP,
    uploadPDF
};
