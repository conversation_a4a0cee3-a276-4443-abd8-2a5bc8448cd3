const Untils = require('./untils')
const pbottleRPA = require('./pbottleRPA')
const xlsx = require("node-xlsx");
const {readyToRPAToLogin, getCookie} = require('./bridge2.js');
const workSheetsFromFile = xlsx.parse(`${__dirname}\\配置项.xlsx`);
const config_sheet = workSheetsFromFile[0].data;
const global_download_path = config_sheet[1][2];
const { addTaxer, getTwiceQRCode, getAddTaxerResult } = require('./AddTaxer.js')
const { taxerConfirmLogin, commitNaturePerson, confirmTaxer, getConfirmTaxerResult } = require('./TaxerConfirm.js')
const { getAddMobileEWM, getAddMobileEWMResult, enterMobile, getAddMobileResult } = require('./AddMobile.js')
const { downloadEXCEL, getPDF, downloadPDF, formatDate } = require('./Download.js')

//全局报错信息
let globalLogMsg = '';
// 验证码
let smsCode = ''
// 小号
let telX = ''
// 异常信息
let coderr = ''
class ExtendClass {
	constructor(proItem, pbottleRPA, childrenTask) {
		// proItem 省份信息
		//pbottleRPA 机器人实例
		//childrenTask子任务信息
		this.proItem = proItem; // 将传入的值存储在类的属性中
		this.childrenTask = childrenTask; // 将传入的值存储在类的属性中
		this.pbottleRPA = pbottleRPA
		this.untils = new Untils()

		this.newtaskId = ''
	}

	async rpa_login() {
		
		// rpa_to_login
		console.log('进入rpa_to_login')
		await readyToRPAToLogin(this.proItem, this.pbottleRPA, this.childrenTask)
		this.pbottleRPA.sleep(1000)

		console.log('进入rpa_login')
		console.log('JSON.parse(this.childrenTask.variable)',JSON.parse(this.childrenTask.variable))
		let ready = await this.untils.waitImage("/input/1920/loginready.png")
		this.pbottleRPA.moveMouseSmooth(ready.x, ready.y)
		this.pbottleRPA.mouseClick() //focus
		this.pbottleRPA.keyTap('ctrl+a')
		this.pbottleRPA.paste(JSON.parse(this.childrenTask.variable).nsrsbh)
		this.pbottleRPA.keyTap('tab')
		this.pbottleRPA.keyTap('ctrl+a')
		this.pbottleRPA.paste(JSON.parse(this.childrenTask.variable).account)
		this.pbottleRPA.keyTap('tab')
		this.pbottleRPA.keyTap('ctrl+a')
		this.pbottleRPA.paste(JSON.parse(this.childrenTask.variable).password)
		this.untils.completeTask(
			this.childrenTask.id,
			this.childrenTask.flowId,
			this.childrenTask.flowKey,
			this.childrenTask.taskKey,
			this.childrenTask.variable,
			1,
			`账密输入成功`
		)
		this.untils.addLog(global.traceId, '账密输入成功', 'rpa_login')
	}

	// 提交图片给前端
	async update_verifyImage() {

		console.log('开始由RPA获取滑块并验证')
		this.untils.addLog(global.traceId, '开始由RPA获取滑块并验证', 'update_verifyImage')
		let isVeryfy = false
		let index = 1
		for(index; index <= 5; index++){
			let res = await this.getVerifyImage()
			const getCapDetialRes  = this.untils.getCapDetial(res.slider_block,res.slider_back)
			console.log('得到的结果',getCapDetialRes.captcha_solution2.target[0])
			let distance = getCapDetialRes.captcha_solution2.target[0]
			let slider  = await this.untils.waitImage('/input/1920/slider.png')
			console.log('滑块验证',slider)
			this.pbottleRPA.moveMouseSmooth(slider.x,slider.y)
			this.pbottleRPA.mouseLeftDragTo(slider.x + distance + 5,slider.y)
			this.pbottleRPA.sleep(1000)
			globalLogMsg = await pbottleRPA.browserCMD_text(`body > div.el-message.el-message--warning.el-icon-warning-color > p`)
			const isTips = await this.untils.existImage2('/input/1920/tips.png')
			const sliderComfirm = await this.untils.existImage2('/input/1920/slider.png')
			const loginBTRes = await this.untils.existImage2('/input/1920/loginBT.png')
			const closeeye = await this.untils.existImage2('/input/1920/closeeye.png')
			console.log('isTips',isTips)
			console.log('sliderComfirm',sliderComfirm)
			console.log('loginBTRes', loginBTRes)
			console.log('closeeye',closeeye)
			if(isTips){
				globalLogMsg = '已连续4次输入个人用户密码错误。如果再次输入错误，您的账户会被锁定！'
				const res  = pbottleRPA.browserCMD_text('body > div.el-message-box__wrapper > div > div.el-message-box__content > div.el-message-box__container > div ')
				if(res.includes('自动解锁')){
					globalLogMsg = '连续认证错误次数过多，您的账号已被锁定。'
				}
			}
			if(sliderComfirm ){
				console.log('验证失败')
				this.pbottleRPA.moveMouseSmooth(200,200)
				this.pbottleRPA.mouseClick()
				this.pbottleRPA.sleep(500)
				this.untils.addLog(global.traceId, `第${index}次滑块验证失败`, 'update_verifyImage')
				if(index == 5){
					break
				}
			}
			if((loginBTRes && closeeye) || isTips){
				// 还有登录按钮说明账密输入有误，需要重新输入
				this.untils.terminateTask(
					this.childrenTask.flowId,
					3,
					this.childrenTask.variable,
					globalLogMsg
				)
				this.untils.addLog(global.traceId, `第${index}次滑块验证失败，页面报错：${globalLogMsg}`, 'update_verifyImage')
				break
			}
			// 验证成功跳出循环
			if(!sliderComfirm && (!loginBTRes || !closeeye)){
				console.log('验证成功,'+`尝试了${index}次`)
				isVeryfy = true
				const res1 = await this.untils.completeTask(
					this.childrenTask.id,
					this.childrenTask.flowId,
					this.childrenTask.flowKey,
					this.childrenTask.taskKey,
					this.childrenTask.variable,
					1,
					`验证成功`
				)
				console.log('完成RPA上传滑块节点',res1)
				this.untils.addLog(global.traceId, `第${index}次滑块验证成功,完成RPA上传滑块节点`, 'update_verifyImage')
				const res2 = await this.untils.completeTask(
					res1.data,
					this.childrenTask.flowId,
					this.childrenTask.flowKey,
					'user_verify',
					this.childrenTask.variable,
					1,
					`验证成功`
				)
				console.log('完成用户滑动滑块节点',res2)
				this.untils.addLog(global.traceId, '完成用户滑动滑块节点', 'user_verify')
				const res3 = await this.untils.completeTask(
					res2.data,
					this.childrenTask.flowId,
					this.childrenTask.flowKey,
					'check_verifyImage',
					this.childrenTask.variable,
					1,
					`验证成功`
				)
				console.log('完成滑块验证节点',res3)
				this.untils.addLog(global.traceId, '完成滑块验证节点', 'check_verifyImage')
				// const res4 = await this.untils.completeTask(
				// 	res3.data,
				// 	this.childrenTask.flowId,
				// 	this.childrenTask.flowKey,
				// 	'check_account',
				// 	this.childrenTask.variable,
				// 	1,
				// 	`验证成功`
				// )
				// console.log('完成登录验证节点',res4)
				// this.untils.addLog(global.traceId, '完成登录验证节点', 'check_account')
				break
			}
		}

		// RPA验证如果没有成功，则走用户验证
		if(index == 5 && !isVeryfy){
			console.log('RPA验证失败，开始用户验证')
			this.untils.addLog(global.traceId, 'RPA验证失败，开始用户验证', 'update_verifyImage')
			const res = await this.getVerifyImage()
			const top  = await pbottleRPA.browserCMD_css(`#slideVerify > div:nth-child(1) > img`,'top') 
			// console.log('高度为', top)
			// console.log('背景图片为', res.slider_back)
			// console.log('滑块为',res.slider_block)
			await this.untils.completeTask(
				this.childrenTask.id,
				this.childrenTask.flowId,
				this.childrenTask.flowKey,
				this.childrenTask.taskKey,
				{slider_back:`${res.slider_back}`,slider_block:`${res.slider_block}`,top:`${top}`},
				1,
				`图片上传成功`
			)
			this.untils.addLog(global.traceId, `图片上传成功，top:${top}，slider_back:${res.slider_back}，slider_block:${res.slider_block}`, 'rpa_login')		
		}
	}

	// 滑块验证
	async check_verifyImage() {
		const res =  await this.untils.getDistanceAndSize(this.childrenTask.flowId)
		this.pbottleRPA.sleep(2000)
		let slider  = await this.untils.waitImage('/input/1920/slider.png')
		console.log('滑块验证',slider)
		this.pbottleRPA.moveMouseSmooth(slider.x,slider.y)
		this.pbottleRPA.mouseLeftDragTo(slider.x + res.data.sliderDistance * 360 / res.data.sliderWidth + 5, slider.y)
		this.pbottleRPA.sleep(1000)
		globalLogMsg = await pbottleRPA.browserCMD_text(`body > div.el-message.el-message--warning.el-icon-warning-color > p`)
		const isTips = await this.untils.existImage2('/input/1920/tips.png')
		const sliderComfirm = await this.untils.existImage2('/input/1920/slider.png')
		const loginBTRes = await this.untils.existImage2('/input/1920/loginBT.png')
		if(isTips){
			globalLogMsg = '已连续4次输入个人用户密码错误。如果再次输入错误，您的账户会被锁定！'
			const res  = pbottleRPA.browserCMD_text('body > div.el-message-box__wrapper > div > div.el-message-box__content > div.el-message-box__container > div ')
			if(res.includes('自动解锁')){
				globalLogMsg = '连续认证错误次数过多，您的账号已被锁定。'
			}
		}
		console.log('isTips',isTips)
		console.log('sliderComfirm',sliderComfirm)
		console.log('loginBTRes', loginBTRes)
		if(!sliderComfirm && !loginBTRes){
			this.untils.completeTask(
				this.childrenTask.id,
				this.childrenTask.flowId,
				this.childrenTask.flowKey,
				this.childrenTask.taskKey,
				this.childrenTask.variable,
				1,
				`滑块验证成功`
			)
			this.untils.addLog(global.traceId, '用户操作滑块验证成功', 'check_verifyImage')
			return
		}
		if(sliderComfirm){
			this.untils.completeTask(
				this.childrenTask.id,
				this.childrenTask.flowId,
				this.childrenTask.flowKey,
				this.childrenTask.taskKey,
				this.childrenTask.variable,
				0,
				`滑块验证失败，请重试`
			)
			this.untils.addLog(global.traceId, '用户操作滑块验证失败，请重试', 'check_verifyImage')
			this.pbottleRPA.moveMouseSmooth(200,200)
			this.pbottleRPA.mouseClick()
			return
		}
		if(loginBTRes){
			this.untils.completeTask(
				this.childrenTask.id,
				this.childrenTask.flowId,
				this.childrenTask.flowKey,
				this.childrenTask.taskKey,
				this.childrenTask.variable,
				1,
				`滑块验证成功`
			)
			this.untils.addLog(global.traceId, '用户操作滑块验证成功', 'check_verifyImage')
			return
		}

	}

	// 验证账密是否正确
	async check_account() {
		// 这块要放在滑块验证成功之后
		// this.logMsg = await pbottleRPA.browserCMD_text(`body > div.el-message.el-message--warning.el-icon-warning-color > p`)
		// 如果当前实例的logMsg为空，尝试使用全局变量
		// if (!this.logMsg || this.logMsg === 'ok') {
		// 	this.logMsg = globalLogMsg
		// }
		console.log('globalLogMsg', globalLogMsg)
		if (globalLogMsg != 'ok' && globalLogMsg != '20s超时') {
			console.log("验证有误", globalLogMsg)
			this.untils.terminateTask(
					this.childrenTask.flowId,
					3,
					this.childrenTask.variable,
					globalLogMsg
				)
			this.untils.addLog(global.traceId, `账密输入有误,报错信息：${globalLogMsg}`, 'check_account')
		}else{
			if(this.childrenTask.flowKey == 'no_code'){
				const newUser = await this.untils.existImage2('/input/1920/newUser.png')
				console.log('newUser',newUser)
				this.untils.addLog(global.traceId, `是否初次登录地方税局:${newUser}`, 'check_account')
				if(newUser){
					// this.pbottleRPA.browserCMD_click('body > div:nth-child(13) > div > div.el-dialog__body > div > div > label > span > span')
					this.pbottleRPA.browserCMD_click('body > div.el-dialog__wrapper > div > div.el-dialog__footer > div > button')
				}
				const isTips = await this.untils.existImage2('/input/1920/tips.png')
				console.log('isTips', isTips)
				this.untils.addLog(global.traceId, `是否初次绑定为该企业的办税员:${isTips}`, 'check_account')
				if(isTips){
					this.pbottleRPA.browserCMD_click('body > div.el-message-box__wrapper > div > div.el-message-box__btns > button')
				}
				// 在此节点检查登录身份,检查身份后直接完成
				console.log('进入select_sf')
				let select_sfRes = await this.select_sf()
				console.log('select_sf' , select_sfRes)

				console.log('完成RPA登录税局节点111')
			}else{
				// 需要验证码
				this.untils.completeTask(
					this.childrenTask.id,
					this.childrenTask.flowId,
					this.childrenTask.flowKey,
					this.childrenTask.taskKey,
					this.childrenTask.variable,
					1,
					`账密输入正确`
				)
				this.untils.addLog(global.traceId, '账密输入正确', 'check_account')
			}
		}
	}
	

	//登录并获取短信验证码， 
	async get_code() {
		// 兼容添加办税员无验证码场景
		let dq = this.proItem.url
		console.log('dq', dq)
		this.untils.addLog(global.traceId,`获取短信验证码的省份为：${dq}`, 'get_code')
		if(dq !== 'zhejiang' && dq !== 'tianjin'){
			this.pbottleRPA.sleep(1000)
			this.pbottleRPA.browserCMD_click(`div span:contains(短信验证)`)
			this.pbottleRPA.browserCMD_click(`button span:contains(获取验证码)`)
			let codeerr = this.pbottleRPA.browserCMD_text(
				`body > div.el-message.el-message--warning.el-icon-warning-color > p`)
			console.log('codeerr', codeerr)
			if (codeerr.includes("过于频繁") || codeerr.includes("超限")) {
				//验证失败 过于频发发送验证码，当前链接任务无效，需重新启动任务
				let rollBackRes = this.untils.terminateTask(
					this.childrenTask.flowId,
					3,
					this.childrenTask.variable,
					codeerr
				)
				console.log('rollBackRes', rollBackRes)
				return
			}
			//完成用户输入账密节点
			let res = this.untils.completeTask(
				this.childrenTask.id, 
				this.childrenTask.flowId, 
				this.childrenTask.flowKey, 
				this.childrenTask.taskKey, 
				this.childrenTask.variable, 
				1, 
				"获取验证码成功"
			)
			console.log('完成RPA获取验证码节点', res)
			return
		}else{
			let select = await this.untils.existImage2('/input/1920/sflx.png')
			if (select) {
				this.pbottleRPA.browserCMD_click(`div[aria-label="身份类型选择"] button span`)
			}
			let res1 = await this.untils.completeTask(
				this.childrenTask.id, 
				this.childrenTask.flowId, 
				this.childrenTask.flowKey, 
				this.childrenTask.taskKey, 
				this.childrenTask.variable, 
				1, 
				"无需验证码登录，直接验证登录结果"
			)
			let res2 = await this.untils.completeTask(
				res1.data, 
				this.childrenTask.flowId, 
				this.childrenTask.flowKey, 
				'commit_code', 
				this.childrenTask.variable, 
				1, 
				"无需验证码登录，直接验证登录结果"
			)
			let res3 = await this.untils.completeTask(
				res2.data, 
				this.childrenTask.flowId, 
				this.childrenTask.flowKey, 
				'check_code', 
				this.childrenTask.variable, 
				1, 
				"无需验证码登录，直接验证登录结果"
			)
			return
		}
		
	}

	//校验验证码是否输入正确
	async check_code() {
		for (let index = 0; index <= 300; index++) { //等300秒
			let VeriCode = JSON.parse(this.childrenTask.variable).smsCode
			this.untils.addLog(global.traceId, `收到的短信验证码：${VeriCode}`, 'check_code')
			console.log("验证码", VeriCode)
			if (VeriCode) {
				// pbottleRPA.browserCMD_click(`input[placeholder="请输入短信验证码"] `) 720 428
				let yanzhengma = `input[placeholder="请输入短信验证码"]`
				if (yanzhengma) {
					this.pbottleRPA.browserCMD_click(`input[placeholder="请输入短信验证码"] `)
				}
				this.pbottleRPA.keyTap('ctrl+a')
				this.pbottleRPA.paste(VeriCode)
				this.pbottleRPA.browserCMD_click(`button span:contains(登录)`)
				let touxiang = await this.untils.existImage2('/input/1920/login.png')
				console.log('touxiang', touxiang)
				if(touxiang){
					// 如果登录成功直接完成
					this.untils.completeTask(
						this.childrenTask.id,
						this.childrenTask.flowId,
						this.childrenTask.flowKey,
						this.childrenTask.taskKey,
						this.childrenTask.variable,
						1,
						'登录成功',
					)
					return
				}
				let codeerr = await this.pbottleRPA.browserCMD_text(`div.el-message > p`)
				console.log('codeerr', codeerr)
				this.untils.addLog(global.traceId, `codeerr:${codeerr}`, 'check_code')
				if ( codeerr.includes("错误") || codeerr.includes("失效") || codeerr.includes("频繁")) {
					//验证失败
					let rollBackRes = this.untils.completeTask(
						this.childrenTask.id,
						this.childrenTask.flowId,
						this.childrenTask.flowKey,
						this.childrenTask.taskKey,
						this.childrenTask.variable,
						0,
						codeerr,
					)
					console.log('rollBackRes', rollBackRes)
					return
				} 
				console.log('验证码正确，登录成功')
				const newUser = await this.untils.existImage2('/input/1920/newUser.png')
				console.log('newUser',newUser)
				this.untils.addLog(global.traceId, `是否初次登录地方税局:${newUser}`, 'check_code')
				if(newUser){
					// this.pbottleRPA.browserCMD_click('body > div:nth-child(13) > div > div.el-dialog__body > div > div > label > span > span')
					this.pbottleRPA.browserCMD_click('body > div.el-dialog__wrapper > div > div.el-dialog__footer > div > button')
				}
				const isTips = await this.untils.existImage2('/input/1920/tips.png')
				console.log('isTips', isTips)
				this.untils.addLog(global.traceId, `是否初次绑定为该企业的办税员:${isTips}`, 'check_code')
				if(isTips){
					this.pbottleRPA.browserCMD_click('body > div.el-message-box__wrapper > div > div.el-message-box__btns > button')
				}
				
				break;
			}
			
			if (index == 300) {
				let res = this.untils.terminateTask(
					this.childrenTask.flowId,
					2,
					this.childrenTask.variable,
					'验证码未输入，终止任务'
				)
				this.pbottleRPA.exit('验证码未输入，终止任务')
			}
		}
		// 检查登录身份
		console.log('进入select_sf')
		let select_sfRes = await this.select_sf()
		console.log('select_sf' , select_sfRes)
		
		return {}
	}

	// 保活-获取登录二维码-简易版
	// async commit_qr_code(){
	// 	// rpa_to_login
	// 	console.log('开始进行登录操作')
	// 	let rpa_to_login111 = await readyToRPAToLogin(this.proItem, this.pbottleRPA, this.childrenTask)
	// 	console.log('rpa_to_login' , rpa_to_login111)
	// 	this.pbottleRPA.keyTap('Ctrl + F5')
	// 	this.pbottleRPA.sleep(2000)
	// 	for (let index = 0; index <= 10; index++){
	// 		let imagecode = this.pbottleRPA.browserCMD_attr('div#qrcodeDiv img', 'src')
	// 		if(imagecode !== 'ok'){
	// 			console.log('二维码图片：', imagecode);
	// 			this.untils.addLog(global.traceId, `二维码图片：${imagecode}`, 'commit_qr_code')
	// 			this.checkCodeLogin()
	// 			return
	// 		}
	// 		console.log(`获取二维码中，剩余时间：${10 - index}秒`)
	// 	}
	// }

	// 保活-获取登录二维码
	async commit_qr_code(){
		// rpa_to_login
		console.log('开始进行登录操作')
		let rpa_to_login111 = await readyToRPAToLogin(this.proItem, this.pbottleRPA, this.childrenTask)
		console.log('rpa_to_login' , rpa_to_login111)
		this.pbottleRPA.sleep(2000)

		for (let index = 0; index <= 10; index++) {
			let imagecode = this.pbottleRPA.browserCMD_attr('div#qrcodeDiv img', 'src')
			// let imagecode = this.pbottleRPA.browserCMD_attr('div#qrcodeDiv img', 'data-src')
			// let imagecode = imagecode1111.split(',')[1];
			console.log('二维码图片：', imagecode.substring(0, 50));
			this.untils.addLog(global.traceId, `二维码图片：${imagecode}`, 'commit_qr_code')

			if (imagecode.length > 50) {

				console.log('imagecode', imagecode) 
				let	res = this.untils.httpfpOnceAuth('POST','onceAuth/onceAuth/scanQrcode',{
					'account': JSON.parse(this.childrenTask.variable).account,
					'qrCode': imagecode,
					'nsrsbh': JSON.parse(this.childrenTask.variable).nsrsbh,
				}, null, false)
				this.untils.addLog(global.traceId, `${JSON.stringify(res)}`, 'commit_qr_code')
				console.log('res', res)

				if(res.code == '0'){		
					console.log('获取二维码成功')
					// 新增登录成功的校验
					this.checkCodeLogin()
				}	
				else {
					const res1 = this.untils.terminateTask(
						this.childrenTask.flowId,
						3,
						this.childrenTask.variable,
						`${res.msg}`
					)
					pbottleRPA.exit('登录失败:' + this.childrenTask.name)
				}
				break;
			}
			if (index == 10) {
				const res = this.untils.terminateTask(
					this.childrenTask.flowId,
					3,
					this.childrenTask.variable,
					'未获取到二维码图片'
				)
				this.pbottleRPA.exit('未获取到二维码图片');
			}
		}
		return;
	}

	// 获取登录二维码
	async codelogin() {

		// rpa_to_login
		console.log('开始进行登录操作')
		let rpa_to_login111 = await readyToRPAToLogin(this.proItem, this.pbottleRPA, this.childrenTask)
		console.log('rpa_to_login' , rpa_to_login111)
		this.pbottleRPA.sleep(2000)

		//二维码登录
		for (let index = 0; index <= 10; index++) {
			let imagecode = this.pbottleRPA.browserCMD_attr('div#qrcodeDiv img', 'src')
			// let imagecode = imagecode1111.split(',')[1];
			console.log('二维码图片：', imagecode.substring(0, 50));
			console.log('global.traceId',global.traceId)
			if (imagecode.length > 50) {
				// 完成子任务  //发送二维码
				
				let rs = this.untils.completeTask(
					this.childrenTask.id,
					this.childrenTask.flowId,
					this.childrenTask.flowKey,
					this.childrenTask.taskKey,
					JSON.stringify({qrCode: imagecode}),
					1,
					`获取二维码成功`
				)
				
				if (rs.state !== 'success') {
					this.untils.terminateTask(
						this.childrenTask.flowId,
						2,
						this.childrenTask.variable,
						'发送二维码子任务提交失败'
					)
					pbottleRPA.exit('发送二维码子任务提交失败:' + this.childrenTask.name)
				}
				break;
			}
			if (index == 10) {
				this.untils.terminateTask(
					this.childrenTask.flowId,
					2,
					this.childrenTask.variable,
					'未获取到二维码图片'
				)
				this.pbottleRPA.exit('未获取到二维码图片');
			}
		}
		return;
	}

	checkCodeLogin() {
		// this.untils.loginConfirm()
		let startTime = Date.now();
		let endTime = startTime + 300000; // 设置结束时间为当前时间加上300秒（300000毫秒）
		while (Date.now() < endTime) {
			// 老猫本地
			// let rs = this.untils.http('GET', `operate/getFlowStatus`, null,{
			// 	'flowId':this.childrenTask.flowId,
			// }, true)
			// console.log("rs", rs)

			let rs = this.untils.http('GET', `flow/operate/getFlowStatus`, null,{
				'flowId':this.childrenTask.flowId,
			}, true)
			console.log("rs", rs)


			if (rs.data.status !== 1) {
				const res = this.untils.terminateTask(
					this.childrenTask.flowId,
					3,
					this.childrenTask.variable,
					'任务前端终止'
				)
				this.pbottleRPA.exit('任务前端终止');
			}

			let imagecode = this.pbottleRPA.browserCMD_attr('div#qrcodeDiv img', 'src');
			if (imagecode !== 'ok') {
				console.log('未扫码，剩余时间：', Math.ceil((endTime - Date.now()) / 1000), '秒');
				new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒
			} else {
				let res = this.untils.completeTask(
					this.childrenTask.id, 
					this.childrenTask.flowId, 
					this.childrenTask.flowKey, 
					this.childrenTask.taskKey,
					this.childrenTask.variable, 
					1, 
					"扫码成功，正在验证中···"
				);
				console.log('完成结果', res);
				return {}; // 返回空对象表示任务完成
			}
		}

		let res = this.untils.terminateTask(
			this.childrenTask.flowId,
			2,
			this.childrenTask.variable,
			'用户未扫码，终止任务'
		)
		this.pbottleRPA.exit('用户未扫码，终止任务');

		return {}; // 如果循环结束仍未完成任务，返回空对象
	}


	async commit_login_result() {
		//判断有没有登录成功，这里默认成功，直接完成，实际上应判断页面元素，如每省份不同，因单独写
		const touxiang = await this.untils.existImage('/input/1920/touxiang.png')
		if(touxiang){
			// if(isGLY && childrenTask.flowKey == 'add_taxer') {
			// 	let res = this.untils.completeTask(
			// 	this.childrenTask.id, 
			// 	this.childrenTask.flowId,
			// 	this.childrenTask.flowKey,
			// 	this.childrenTask.taskKey, 
			// 	this.childrenTask.variable, 
			// 	1, 
			// 	"登录成功"
			// 	)
			// 	console.log('管理员登陆，完成结果', res)
			// 	return 
			// }
	
			let qymc1 = JSON.parse(this.childrenTask.variable).qymc
			let qymc2 = ''
			qymc2 = this.pbottleRPA.browserCMD_text(`div[class="leftTopItem clearfix"] div:nth-child(1)`)
			qymc2 = this.pbottleRPA.browserCMD_text(`div[class="leftTopItem clearfix"] div:nth-child(1)`)
			qymc2 = this.pbottleRPA.browserCMD_text(`div[class="leftTopItem clearfix"] div:nth-child(1)`)
			if(qymc2 == 'ok'){
				pbottleRPA.browserCMD_click('#app > div > div.content.el-row.el-row--flex > div.main-container.el-col.el-col-19.page-no-shadow > section > div > div.first-card > div:nth-child(2) > div.enterprise-name > img')
				this.pbottleRPA.sleep(1000)
				qymc2 = pbottleRPA.browserCMD_text('#app > div > div.content.el-row.el-row--flex > div.main-container.el-col.el-col-19.page-no-shadow > section > div > div.first-card > div:nth-child(2) > div.enterprise-name > span')
			}
			console.log('当前登录企业名称-', qymc1, qymc2)
			if (qymc1 != qymc2.trim()) {
	
				let res = this.untils.terminateTask(
					this.childrenTask.flowId,
					3,
					this.childrenTask.variable,
					'授权失败,税号公司名称不匹配，请检查！请打开电子税务局APP，在首页顶部找到并点击【身份切换】功能，选择相应的企业身份后，重新执行您的操作。'
				)
				console.log('res', res)
				
				this.pbottleRPA.keyTap('Ctrl + Shift + W');
				this.pbottleRPA.exit('授权失败,税号公司名称不匹配，终止任务');
				return 1
			} else {
				let res = this.untils.completeTask(
					this.childrenTask.id, 
					this.childrenTask.flowId,
					this.childrenTask.flowKey,
					this.childrenTask.taskKey, 
					this.childrenTask.variable, 
					 1, 
					 "登录成功"
					)
				console.log('完成结果', res)
				return 1
			}
		}else{
			pbottleRPA.keyTap('F5')
			pbottleRPA.sleep(1000)
			await this.commit_login_result()
		}
		
	}

	async gather_invoice() {

		this.pbottleRPA.sleep(5000)
		console.log("=====开始获取财税Cookie=======")
		let cookie = await this.getcookie(this.proItem)
		console.log('Cookie获取完成',cookie)
		this.pbottleRPA.sleep(2000)

		this.pbottleRPA.openURL(
			`https://tpass.${this.proItem.url}.chinatax.gov.cn:8443/#/userCenterEP/baseInfoEP`)
		let geren = await this.untils.waitImage("/input/1920/geren.png")
		if (geren) {
			this.pbottleRPA.browserCMD_click(`div[class="enterprise-name"] img[title="显示完整"]`)
		}

		var myUsername = this.pbottleRPA.browserCMD_text(`div[class="enterprise-name"] span`)
		console.log(",,,,,,,,,,", myUsername)


		console.log('进入gather_invoice')
		this.pbottleRPA.openURL(
			`https://dppt.${this.proItem.url}.chinatax.gov.cn:8443/invoice-query/invoice-query/`)
		let downloadTime = new Date().getTime();
		var count = 0
		let chaxuntype = await this.untils.waitImage("/input/1920/chaxuntype.png")
		if (chaxuntype) {
			this.pbottleRPA.moveMouseSmooth(chaxuntype.x, chaxuntype.y)
		}
		//广东省循环逐月采集 ，其他省份逐年采集
		if(this.proItem.url === 'guangdong' ){
			console.log(`开始逐月采集，当前省份为${this.proItem.url}`)
			this.untils.addLog(global.traceId, `开始逐月采集，当前省份为${this.proItem.url}`, 'gather_invoice')
			for (let index = 0; index < 2; index++) {
				if (index == 1) {
					//chaxuntype.png
					this.pbottleRPA.moveMouseSmooth(chaxuntype.x + 150, chaxuntype.y)
					this.pbottleRPA.mouseClick() //focus
					this.pbottleRPA.browserCMD_click(`li span:contains(取得发票)`)
				}

				const fplx = (index === 0) ? '销项' : '进项';
				// 获取当前日期
				var currentDate = new Date();

				// 获取当前日期的上一个月
				currentDate.setMonth(currentDate.getMonth() - 1);
				// 循环输出36个月的数据
				for (var i = 1; i <= 36; i++) {
					let currentCount = 0;
					if (index == 1 && count != currentCount) {
						//chaxuntype.png
						currentCount = count
						this.pbottleRPA.moveMouseSmooth(chaxuntype.x + 150, chaxuntype.y)
						this.pbottleRPA.mouseClick() //focus
						this.pbottleRPA.browserCMD_click(`li span:contains(取得发票)`)
					}
					// 获取上个月的日期
					currentDate.setDate(1);
					var startDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);

					// 获取当前月份的最后一天
					var endDate = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);

					// 输出日期范围
					console.log(formatDate(startDate) + '---------->' + formatDate(endDate));

					// 将当前日期往前推1个月
					currentDate.setMonth(currentDate.getMonth() - 1);

					this.pbottleRPA.browserCMD_click(`input[placeholder="开票日期起"]`)
					this.pbottleRPA.browserCMD_val('input[placeholder="开票日期起"]', formatDate(startDate));
					this.pbottleRPA.keyTap('Enter')

					this.pbottleRPA.browserCMD_click(`input[placeholder="开票日期止"]`)
					this.pbottleRPA.browserCMD_val('input[placeholder="开票日期止"]', formatDate(endDate));
					this.pbottleRPA.keyTap('Enter')

					this.pbottleRPA.browserCMD_click(`button span:contains(查询)`)
					console.log("-------------查询结束---------------")
					this.pbottleRPA.sleep(1000)
					let daochu = this.pbottleRPA.browserCMD_text('div[class="button__export"] > button > span:nth-child(1)')
					if (daochu === 'ok') {
						console.log("-------------查询无发票数据--------------")
						this.untils.addLog(global.traceId, `${formatDate(startDate)}` + '---' + `${formatDate(endDate)}` + "年度无" +`${fplx}` +"数据", 'gather_invoice')
						console.log(`${formatDate(startDate)}` + '---' + `${formatDate(endDate)}` + "年度无" +
							`${fplx}` +
							"数据")
					}else{
						this.pbottleRPA.sleep(500)
						let text = this.pbottleRPA.browserCMD_text(
							`div[class="t-pagination__total"]`) //div[class = "statistics-info"] span:nth-child(1)
						let match = text.match(/\d+/);
						let number = match ? parseInt(match[0]) : 0;
						console.log("===============", text, number)
						this.pbottleRPA.browserCMD_click(`button span:contains(导出 )`)
						this.pbottleRPA.browserCMD_click(`li span:contains(导出全部)`)
						let downloading = await this.untils.existImage("/input/1920/dowloading.png")
						// let wxts = this.untils.waitImage("/input/1920/wxts.png")
						if (downloading) {
							console.log("-------------直接开始下载---------------")
						} else if (!downloading) {
							let submissionTimes = [];
							count++;
							// 发票数量大于2500，异步下载
							console.log("-------------提交异步下载---------------")
							if (!downloading) {
								console.log("温馨提示")
								let wxts1 = await this.untils.waitImage("/input/1920/wxts.png")
								this.pbottleRPA.sleep(500)
								this.pbottleRPA.moveMouseSmooth(wxts1.x + 20, wxts1.y + 180)
								this.pbottleRPA.mouseClick() //focus
							}
							this.untils.addLog(global.traceId, `${formatDate(startDate)}` + '---' + `${formatDate(endDate)}` +`${fplx}` +"数据提交异步下载", 'gather_invoice')
							this.pbottleRPA.sleep('30000');
							this.pbottleRPA.keyTap('alt + Left')
						}
						let down1 = await this.untils.waitImageDisappear("/input/1920/dowloading.png")
						if (down1) {
							console.log("-------------已下载---------------")
							this.untils.addLog(global.traceId, `${formatDate(startDate)}` + '---' + `${formatDate(endDate)}`+`${fplx}` + '数据下载完成', 'gather_invoice')
							this.pbottleRPA.sleep(2000)
						}
					}

				}
			}
		}else {
			console.log(`开始逐年采集，当前省份为${this.proItem.url}`)
			for (let index = 0; index < 2; index++) {
				this.untils.addLog(global.traceId, `开始逐年采集，当前省份为${this.proItem.url}`, 'gather_invoice')
				if (index == 1) {
					//chaxuntype.png
					this.pbottleRPA.moveMouseSmooth(chaxuntype.x + 150, chaxuntype.y)
					this.pbottleRPA.mouseClick() //focus
					this.pbottleRPA.browserCMD_click(`li span:contains(取得发票)`)
				}

				const fplx = (index === 0) ? '销项' : '进项';
				// 获取当前日期
				var currentDate = new Date();

				// 获取当前日期的上一个月
				currentDate.setMonth(currentDate.getMonth() - 1);
				// 循环输出3年的数据
				for (var i = 1; i <= 3; i++) {
					let currentCount = 0;
					if (index == 1 && count != currentCount) {
						//chaxuntype.png
						currentCount = count
						this.pbottleRPA.moveMouseSmooth(chaxuntype.x + 150, chaxuntype.y)
						this.pbottleRPA.mouseClick() //focus
						this.pbottleRPA.browserCMD_click(`li span:contains(取得发票)`)
					}
					// 获取11个月前的日期
					var startDate = new Date(currentDate.getFullYear(), currentDate.getMonth() - 11, 1);

					// 获取当前月份的最后一天
					var endDate = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);

					// 输出日期范围
					console.log(formatDate(startDate) + '---------->' + formatDate(endDate));

					// 将当前日期往前推12个月
					currentDate.setMonth(currentDate.getMonth() - 12);

					this.pbottleRPA.browserCMD_click(`input[placeholder="开票日期起"]`)
					this.pbottleRPA.browserCMD_val('input[placeholder="开票日期起"]', formatDate(startDate));
					this.pbottleRPA.keyTap('Enter')

					this.pbottleRPA.browserCMD_click(`input[placeholder="开票日期止"]`)
					this.pbottleRPA.browserCMD_val('input[placeholder="开票日期止"]', formatDate(endDate));
					this.pbottleRPA.keyTap('Enter')

					this.pbottleRPA.browserCMD_click(`button span:contains(查询)`)
					console.log("-------------查询结束---------------")
					this.pbottleRPA.sleep(1000)
					let daochu = this.pbottleRPA.browserCMD_text('div[class="button__export"] > button > span:nth-child(1)')
					if(daochu === 'ok') {
							console.log("-------------查询无发票数据--------------")
							console.log(`${formatDate(startDate)}` + '---' + `${formatDate(endDate)}` + "年度无" +
								`${fplx}` +
								"数据")
							this.untils.addLog(global.traceId, `${formatDate(startDate)}` + '---' + `${formatDate(endDate)}` + "年度无" +`${fplx}` +"数据", 'gather_invoice')
						}
					else{
						this.pbottleRPA.sleep(500)
						let text = this.pbottleRPA.browserCMD_text(
							`div[class="t-pagination__total"]`) //div[class = "statistics-info"] span:nth-child(1)
						let match = text.match(/\d+/);
						let number = match ? parseInt(match[0]) : 0;
						console.log("===============", text, number)
						this.pbottleRPA.browserCMD_click(`button span:contains(导出 )`)
						this.pbottleRPA.browserCMD_click(`li span:contains(导出全部)`)
						let downloading = await this.untils.existImage("/input/1920/dowloading.png")
						// let wxts = this.untils.existImage("/input/1920/wxts.png")
						// let wxts = this.untils.waitImage("/input/1920/wxts.png")
						if (downloading) {
							console.log("-------------直接开始下载---------------")
						} else if (!downloading) {
							let submissionTimes = [];
							count++;
							// 发票数量大于2500，异步下载
							console.log("-------------提交异步下载---------------")
							if (!downloading) {
								console.log("温馨提示")
								let wxts1 = await this.untils.waitImage("/input/1920/wxts.png")
								this.pbottleRPA.sleep(500)
								this.pbottleRPA.moveMouseSmooth(wxts1.x + 20, wxts1.y + 180)
								this.pbottleRPA.mouseClick() //focus
							}
							this.untils.addLog(global.traceId, `${formatDate(startDate)}` + '---' + `${formatDate(endDate)}` +`${fplx}` +"数据提交异步下载", 'gather_invoice')
							this.pbottleRPA.sleep('30000');
							this.pbottleRPA.keyTap('alt + Left')
						}
						let down1 = await this.untils.waitImageDisappear("/input/1920/dowloading.png")
						if (down1) {
							console.log("-------------已下载---------------")
							this.untils.addLog(global.traceId, `${formatDate(startDate)}` + '---' + `${formatDate(endDate)}`+`${fplx}` + '数据下载完成', 'gather_invoice')
							this.pbottleRPA.sleep(2000)
						}
					}

				}
			}
		}
		console.log("count+++++++++++++++++++++++++++++", count)

		if (count > 0) {
			const startTime = Date.now(); // 记录开始时间
			await downloadEXCEL(count,downloadTime,myUsername,this.proItem.url)

			const currentTime = Date.now();
			const elapsedTime = Math.floor((currentTime - startTime) / 1000); // 计算已经过去的时间（秒）
			if (elapsedTime >= 900) {
				console.log("时间太长了")
				let res = this.untils.completeTask(
					this.childrenTask.id,
					this.childrenTask.flowId, 
					this.childrenTask.flowKey, 
					this.childrenTask.taskKey,
					this.childrenTask.variable, 
					1, 
					"提交发票超时"
				)
				console.log('完成结果', res)
			}
		}

		await this.untils.checkDownloadedFiles();
		let file_path = global_download_path + '/../dataRPA.zip';
		// let file = await this.untils.uploadFile(0)
		this.pbottleRPA.sleep(3000)
		let file = await this.untils.uploadToOSS(file_path)
		this.untils.addLog(global.traceId, `${file}`, 'gather_invoice')
		console.log('file', file)
		//老猫本地
		// let path = this.untils.httpfp('POST', 'callback/rpa/analysis', {
		// 	'collectType': 1,
		// 	'fileUrl': file,
		// 	'requestId': this.childrenTask.requestId

		// }, null, false)
		let path = ''
		try{
			 path = this.untils.httpfp('POST', 'taxmanage/callback/rpa/analysis', {
				'collectType': 1,
				'fileUrl': file,
				'requestId': this.childrenTask.requestId
			}, null, false)
			this.untils.addLog(global.traceId, `${JSON.stringify(path)}`, 'gather_invoice')
		}catch(error){
			this.untils.addLog(global.traceId, `${error.message}`, 'gather_invoice')
			console.error(error.message);
		}

		if (path.code == 500) {
			console.log('path------------', path)
			let res = this.untils.terminateTask(
				this.childrenTask.flowId,
				3,
				this.childrenTask.variable,
				'发票解析失败'
			)
			this.pbottleRPA.exit('发票解析失败:' + this.childrenTask.name)
		}

		let res = this.untils.completeTask(
			this.childrenTask.id, 
			this.childrenTask.flowId, 
			this.childrenTask.flowKey, 
			this.childrenTask.taskKey,
			this.childrenTask.variable, 
			1, 
			"提交发票成功"
		)
		if (res.state == 'fail') {
			this.pbottleRPA.exit('子任务提交失败:' + this.childrenTask.name)
		}
		return
	}

	async download_pdf(){
		await getPDF(this.proItem, this.childrenTask)
	}

	async add_taxer(){
		const res = await addTaxer(this.proItem, this.childrenTask)
		coderr = res
		console.log('addTaxer', res)
	}

	async get_twice_qrCode(){
		await getTwiceQRCode(this.childrenTask, coderr)
	}

	async get_add_taxer_result () {
		await getAddTaxerResult(this.childrenTask)
	}

	// 新办税员确认-RPA登录
	async taxerConfirm_login() {
		console.log('进入taxerConfirmLogin')
		await taxerConfirmLogin(this.proItem, this.childrenTask)
	}
	
	// 新办税员确认-RPA验证自然人登录结果
	async commit_nature_person(){
		console.log('开始确认税务局人员')
		await commitNaturePerson(this.childrenTask)
	}

	// 新办税员确认-RPA确认办税员并获取二维码
	async confirm_taxer(){
		await confirmTaxer(this.childrenTask)
	}

	// 新办税员确认-RPA获取扫描结果
	async get_confirm_taxer_result(){
		await getConfirmTaxerResult(this.childrenTask)
	}

	// 添加手机号码-获取添加手机号人脸二维码
	async get_add_mobile_ewm(){
		await getAddMobileEWM(this.proItem, this.childrenTask)
	}

	// 添加手机号码-获取扫描结果
	async get_add_mobile_ewm_result(){
		await getAddMobileEWMResult(this.childrenTask)
	}

	// 添加手机号码-获取扫描结果
	async enter_mobile(){
		await enterMobile(this.childrenTask)
	}

	// 添加手机号码-获取扫描结果
	async get_add_mobile_result(){
		await getAddMobileResult(this.childrenTask)
	}

	async getcookie(proItem){
		/*
		* 发票采集之前，获取Cookie传给后台--------lufei
		* */
		let cookie = ''
		let cookie1 = ''
		let cookie2 = ''
		let cookie3 = ''
		let cookie4 = ''
		let rs = await this.untils.waitImage("/input/1920/swszzh.png")
		this.pbottleRPA.moveAndClick(rs.x, rs.y)
		let error =  this.pbottleRPA.browserCMD_text(`div[class="t-message t-is-error"]`)
		console.log('error:',error)
		if(error === 'ok' ||  error === '20s超时' ){
			this.pbottleRPA.sleep(5000)
			let CSQYMC = this.pbottleRPA.browserCMD_text(`div[class="title"]`)
			console.log(CSQYMC)
			if(CSQYMC !== 'ok'){
				// 先通过代码获取Cookie
				let getCookiesFromRPA = getCookie(proItem)
				cookie1 = getCookiesFromRPA.cookie1
				cookie2 = getCookiesFromRPA.cookie2
				console.log(cookie1,cookie2)
				if(cookie1 && cookie2 ){
					console.log("由小瓶获取Cookie")
					this.untils.addLog(global.traceId, `从小瓶开始获取cookie`, 'getcookie')
					cookie3 = pbottleRPA.browserCMD_cookie(`${cookie1}`)
					cookie4 = pbottleRPA.browserCMD_cookie(`${cookie2}`)
					console.log(cookie3,cookie4)
					cookie = `${cookie1}=${cookie3}; ${cookie2}=${cookie4}`
					console.log('由小瓶获取的cookie',cookie)
					this.untils.addLog(global.traceId, `由小瓶获取的cookie：${cookie}`, 'getcookie')
				}else {
					console.log("由篡改猴获取Cookie")
					this.untils.addLog(global.traceId, `从篡改猴开始获取cookie`, 'getcookie')
					cookie = this.pbottleRPA.getClipboard()
					console.log('由篡改猴获取的cookie',cookie)
					this.untils.addLog(global.traceId, `从篡改猴获取的cookie：${cookie}`, 'getcookie')
					if(cookie.includes('failed')){
						console.log("由篡改猴获取Cookie失败！")
						this.untils.addLog(global.traceId, `从篡改猴获取cookie失败`, 'getcookie')
						console.log("开始从控制台获取Cookie")
						this.untils.addLog(global.traceId, `从控制台开始获取cookie`, 'getcookie')
						this.pbottleRPA.sleep(1000)
						this.pbottleRPA.keyTap('Ctrl + Shift + J')
						this.pbottleRPA.sleep(1000)
						let kongzhitai = await this.untils.waitImage("/input/1920/kongzhitai.png")
						this.pbottleRPA.moveMouseSmooth(kongzhitai.x, kongzhitai.y)
						this.pbottleRPA.mouseClick()
						this.pbottleRPA.keyTap('Ctrl+L')
						this.pbottleRPA.moveMouseSmooth(kongzhitai.x, kongzhitai.y+100)
						this.pbottleRPA.mouseClick()
						this.pbottleRPA.sleep(1000)
						this.pbottleRPA.paste(`允许粘贴`)
						this.pbottleRPA.sleep(500)
						this.pbottleRPA.keyTap('ENTER')
						this.pbottleRPA.sleep(500)
						this.pbottleRPA.paste(`let result = document.cookie; console.log(result); copy(result)`)
						this.pbottleRPA.keyTap('ENTER')
						// console.log(pbottleRPA.getClipboard())
						cookie = this.pbottleRPA.getClipboard()
						console.log('由控制台获取的cookie',cookie)
						this.untils.addLog(global.traceId, `从控制台获取的cookie：${cookie}`, 'getcookie')
						this.pbottleRPA.keyTap('ctrl+W')
						}	
				}
			}else {
				this.untils.addLog(global.traceId, 'Cookie获取失败！', 'getcookie')
				console.log("Cookie获取失败！",cookie)
			}
			
		}else{
			this.untils.addLog(global.traceId, `${error}`, 'getcookie')
			console.log('error:',error)
		}

		try {
			console.log('dq',this.proItem.url)
			//老猫本地
			// let getcookie = this.untils.httpfp('POST', 'callback/rpa/collectFinance', {
			// 	'cookie': cookie,
			// 	'requestId': this.childrenTask.requestId,
			// 	'dq':this.proItem.url
			// }, null, false)

			let getcookie = this.untils.httpfp('POST', 'taxmanage/callback/rpa/collectFinance', {
				'cookie': cookie,
				'requestId': this.childrenTask.requestId,
				'dq':this.proItem.url
			}, null, false)
			this.untils.addLog(global.traceId, `${JSON.stringify(getcookie)}`, 'getcookie')
			console.log('getcookie11111', getcookie)

			if (getcookie.code == 500) {
				console.log('getcookie------------', getcookie)
				this.pbottleRPA.exit('子任务提交失败:' + this.childrenTask.name)
			}
		}catch (error){
			this.untils.addLog(global.traceId, `${error.message}`, 'getcookie')
			console.error(error.message);
		}
		return cookie;
	}

	// 选择登录身份
	async select_sf() {
		// value=03：办税员   value=05 管理员  
		this.pbottleRPA.sleep(1000)
		let select = false
		select = await this.untils.existImage2('/input/1920/sflx.png')
		console.log('this.childrenTask.flowKey',this.childrenTask.flowKey)
		if (select) {
			if(this.childrenTask.flowKey.includes('add_taxer')){
				let userSF = '管理员'
				let sflxArrey = []
				for(let index = 1 ; index <= 5 ; index++){
					const res = await pbottleRPA.browserCMD_text(`body > div.el-dialog__wrapper > div > div.el-dialog__body > div > div > div > label:nth-child(${index})`)
					console.log('res', res)
					if(res == 'ok'){
						break;
					}else{
						sflxArrey.push({'id':index,'sflx':res})
					}
				}
				console.log('sflxArrey', sflxArrey)
				this.untils.addLog(global.traceId,`用户拥有的身份：${sflxArrey}`, 'select_sf')
				const cwfzr = sflxArrey.find(item => item.sflx == '财务负责人')
				const fr = sflxArrey.find(item => item.sflx == '法定代表人')
				if(cwfzr || fr){
					console.log('选择法人身份或者财务负责人身份登录')
					this.untils.addLog(global.traceId, `选择法人身份或者财务负责人身份登录`, 'select_sf')
				}else{
					const selextSF =  sflxArrey.filter(item => item.sflx === userSF)
					console.log('selextSF', selextSF)
					if(selextSF.length > 0){
						pbottleRPA.browserCMD_click(`body > div.el-dialog__wrapper > div > div.el-dialog__body > div > div > div > label:nth-child(${selextSF[0].id}) > span.el-radio__input > span`)
					}else{
						console.log(`用户没有【${userSF}】身份,用默认身份登录`)
						this.untils.addLog(global.traceId, `用户没有【${userSF}】身份,用默认身份登录`, 'select_sf')
					}
				}
			}
			this.pbottleRPA.browserCMD_click(`div[aria-label="身份类型选择"] button span`)
			this.pbottleRPA.sleep(3000)
			let res = this.untils.completeTask(
				this.childrenTask.id, 
				this.childrenTask.flowId, 
				this.childrenTask.flowKey, 
				this.childrenTask.taskKey,
				this.childrenTask.variable, 
				1, 
				"选择身份成功"
				)
		} else {
			this.pbottleRPA.browserCMD_click(`div[aria-label="身份类型选择"] button span`)
			this.pbottleRPA.sleep(3000)
			console.log('没找到该图片2')
			let res = this.untils.completeTask(
				this.childrenTask.id, 
				this.childrenTask.flowId, 
				this.childrenTask.flowKey, 
				this.childrenTask.taskKey, 
				this.childrenTask.variable, 
				1, 
				"无需选择身份"
			)
		}
		return 1
	}

	async getVerifyImage() {
		this.pbottleRPA.browserCMD_click(`button span:contains(登录)`)
		let varifyImage = await this.pbottleRPA.browserCMD_attr('div#slideVerify img', 'src')
		// console.log('验证码图片地址', varifyImage)
		let slider_back = ''
		let slider_block = ''
		if(varifyImage != 'ok'){
			slider_back = JSON.parse(varifyImage)[0] 
			slider_block = JSON.parse(varifyImage)[1] 
			// console.log('imagecode[0]',slider_back)
			// console.log('imagecode[1]',slider_block)
		}else{
			slider_back = 'data:image/jpg;base64,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'
			slider_block = 'data:image/png;base64,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'
		}
		return {slider_back,slider_block}
	}

}

module.exports = ExtendClass;
